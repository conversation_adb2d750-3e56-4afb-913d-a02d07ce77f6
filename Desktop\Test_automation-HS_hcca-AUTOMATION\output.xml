<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.3 (Python 3.10.11 on win32)" generated="2025-06-09T12:17:23.001397" rpa="false" schemaversion="5">
<suite id="s1" name="Get" source="C:\Users\<USER>\Desktop\Test_automation-HS_hcca-AUTOMATION\get.robot">
<test id="s1-t1" name="Houston Automation Test" line="61">
<kw name="Clean Environment" type="SETUP">
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-09T12:17:24.457801" level="INFO">Starting process:
taskkill /F /IM houston_server.exe /T</msg>
<msg time="2025-06-09T12:17:24.468802" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-09T12:17:24.698300" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-09T12:17:24.456800" elapsed="0.241500"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-09T12:17:26.712501" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-09T12:17:24.699238" elapsed="2.013263"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-09T12:17:26.714589" level="INFO">✅ Killed process: houston_server.exe</msg>
<arg>✅ Killed process: ${process_name}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-09T12:17:26.713590" elapsed="0.000999"/>
</kw>
<arg>houston_server.exe</arg>
<status status="PASS" start="2025-06-09T12:17:24.454801" elapsed="2.259788"/>
</kw>
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-09T12:17:26.718566" level="INFO">Starting process:
taskkill /F /IM houston_app.exe /T</msg>
<msg time="2025-06-09T12:17:26.733562" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-09T12:17:26.960264" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-09T12:17:26.717584" elapsed="0.242680"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-09T12:17:28.966990" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-09T12:17:26.960264" elapsed="2.006726"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-09T12:17:28.969038" level="INFO">✅ Killed process: houston_app.exe</msg>
<arg>✅ Killed process: ${process_name}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-09T12:17:28.968051" elapsed="0.000987"/>
</kw>
<arg>houston_app.exe</arg>
<status status="PASS" start="2025-06-09T12:17:26.715588" elapsed="2.254450"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-09T12:17:28.971038" level="INFO">🧹 Environment cleaned</msg>
<arg>🧹 Environment cleaned</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-09T12:17:28.970038" elapsed="0.001000"/>
</kw>
<status status="PASS" start="2025-06-09T12:17:24.453802" elapsed="4.517236"/>
</kw>
<kw name="Start Houston Server">
<kw name="Start Process" owner="Process">
<msg time="2025-06-09T12:17:28.976021" level="INFO">Starting process:
C:\Users\<USER>\Desktop\SX-Houston-server_v142\SX-Houston-server_v142\houston_server.exe</msg>
<msg time="2025-06-09T12:17:28.997042" level="INFO">${hs_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hs_handle}</var>
<arg>${HS_PATH}</arg>
<arg>shell=True</arg>
<arg>cwd=${HS_DIR}</arg>
<doc>Starts a new process on background.</doc>
<status status="PASS" start="2025-06-09T12:17:28.974026" elapsed="0.023016"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-09T12:17:39.003989" level="INFO">Slept 10 seconds.</msg>
<arg>10s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-09T12:17:28.998041" elapsed="10.006952"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-09T12:17:39.005992" level="INFO">🚀 Houston Server started successfully</msg>
<arg>🚀 Houston Server started successfully</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-09T12:17:39.004993" elapsed="0.000999"/>
</kw>
<return>
<value>${hs_handle}</value>
<status status="PASS" start="2025-06-09T12:17:39.007061" elapsed="0.003989"/>
</return>
<msg time="2025-06-09T12:17:39.011997" level="INFO">${hs} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hs}</var>
<status status="PASS" start="2025-06-09T12:17:28.973026" elapsed="10.038971"/>
</kw>
<kw name="Start HCCA">
<kw name="Start Process" owner="Process">
<msg time="2025-06-09T12:17:39.017061" level="INFO">Starting process:
C:\Users\<USER>\Desktop\SX-Houston-app_v212\houston_app.exe</msg>
<msg time="2025-06-09T12:17:39.037042" level="INFO">${hcca_handle} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hcca_handle}</var>
<arg>${HCCA_PATH}</arg>
<arg>shell=True</arg>
<arg>cwd=${HCCA_DIR}</arg>
<doc>Starts a new process on background.</doc>
<status status="PASS" start="2025-06-09T12:17:39.015063" elapsed="0.021979"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-09T12:17:44.047670" level="INFO">Slept 5 seconds.</msg>
<arg>5s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-09T12:17:39.037992" elapsed="5.009678"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-09T12:17:44.048701" level="INFO">🚀 HCCA started successfully</msg>
<arg>🚀 HCCA started successfully</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-09T12:17:44.048701" elapsed="0.000000"/>
</kw>
<return>
<value>${hcca_handle}</value>
<status status="PASS" start="2025-06-09T12:17:44.049744" elapsed="0.000000"/>
</return>
<msg time="2025-06-09T12:17:44.049744" level="INFO">${hcca} = &lt;Popen: returncode: None args: 'C:\\Users\\<USER>\\Desktop\\SX-Houst...&gt;</msg>
<var>${hcca}</var>
<status status="PASS" start="2025-06-09T12:17:39.014007" elapsed="5.036740"/>
</kw>
<kw name="Click Search And Type">
<kw name="Open Eyes">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-09T12:17:44.053750" level="INFO">👁️ Opening eyes for visual recognition</msg>
<arg>👁️ Opening eyes for visual recognition</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-09T12:17:44.053750" elapsed="0.000000"/>
</kw>
<status status="PASS" start="2025-06-09T12:17:44.052748" elapsed="0.002001"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-09T12:18:04.064818" level="INFO">Slept 20 seconds.</msg>
<arg>20s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-09T12:17:44.054749" elapsed="20.010069"/>
</kw>
<kw name="Run Keyword And Ignore Error" owner="BuiltIn">
<kw name="Find Element" owner="RPA.Desktop">
<msg time="2025-06-09T12:18:04.067902" level="FAIL">Keyword 'RPA.Desktop.Find Element' expected 1 argument, got 2.</msg>
<arg>${SEARCH_IMAGE}</arg>
<arg>confidence=70</arg>
<doc>Find an element defined by locator, and return its position.
Raises ``ElementNotFound`` if` no matches were found, or
``MultipleElementsFound`` if there were multiple matches.</doc>
<status status="FAIL" start="2025-06-09T12:18:04.066901" elapsed="0.001001">Keyword 'RPA.Desktop.Find Element' expected 1 argument, got 2.</status>
</kw>
<msg time="2025-06-09T12:18:04.068904" level="INFO">${status} = FAIL</msg>
<msg time="2025-06-09T12:18:04.068904" level="INFO">${element} = Keyword 'RPA.Desktop.Find Element' expected 1 argument, got 2.</msg>
<var>${status}</var>
<var>${element}</var>
<arg>Find Element</arg>
<arg>${SEARCH_IMAGE}</arg>
<arg>confidence=70</arg>
<doc>Runs the given keyword with the given arguments and ignores possible error.</doc>
<status status="PASS" start="2025-06-09T12:18:04.065907" elapsed="0.002997"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-09T12:18:19.078944" level="INFO">Slept 15 seconds.</msg>
<arg>15s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-09T12:18:04.069837" elapsed="15.009107"/>
</kw>
<kw name="Type Text" owner="RPA.Desktop">
<arg>get_app_period</arg>
<doc>Type text one letter at a time.</doc>
<status status="PASS" start="2025-06-09T12:18:19.079972" elapsed="0.014425"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-09T12:18:19.096401" level="INFO">✅ Search term typed successfully</msg>
<arg>✅ Search term typed successfully</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-09T12:18:19.095400" elapsed="0.001001"/>
</kw>
<kw name="Close Eyes">
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-09T12:18:19.099397" level="INFO">👁️ Closing eyes after visual operation</msg>
<arg>👁️ Closing eyes after visual operation</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-09T12:18:19.098397" elapsed="0.001000"/>
</kw>
<status status="PASS" start="2025-06-09T12:18:19.097398" elapsed="0.001999"/>
</kw>
<status status="PASS" start="2025-06-09T12:17:44.051710" elapsed="35.048687"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-09T12:18:22.109789" level="INFO">Slept 3 seconds.</msg>
<arg>3s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-09T12:18:19.101397" elapsed="3.008392"/>
</kw>
<kw name="Clean Environment" type="TEARDOWN">
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-09T12:18:22.117882" level="INFO">Starting process:
taskkill /F /IM houston_server.exe /T</msg>
<msg time="2025-06-09T12:18:22.139874" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-09T12:18:22.382222" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-09T12:18:22.116884" elapsed="0.266330"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-09T12:18:24.390685" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-09T12:18:22.383214" elapsed="2.007471"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-09T12:18:24.392760" level="INFO">✅ Killed process: houston_server.exe</msg>
<arg>✅ Killed process: ${process_name}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-09T12:18:24.391711" elapsed="0.001049"/>
</kw>
<arg>houston_server.exe</arg>
<status status="PASS" start="2025-06-09T12:18:22.113882" elapsed="2.279876"/>
</kw>
<kw name="Kill Process If Running">
<kw name="Run Process" owner="Process">
<msg time="2025-06-09T12:18:24.396766" level="INFO">Starting process:
taskkill /F /IM houston_app.exe /T</msg>
<msg time="2025-06-09T12:18:24.415612" level="INFO">Waiting for process to complete.</msg>
<msg time="2025-06-09T12:18:24.665390" level="INFO">Process completed.</msg>
<arg>taskkill</arg>
<arg>/F</arg>
<arg>/IM</arg>
<arg>${process_name}</arg>
<arg>/T</arg>
<arg>shell=True</arg>
<doc>Runs a process and waits for it to complete.</doc>
<status status="PASS" start="2025-06-09T12:18:24.395768" elapsed="0.269622"/>
</kw>
<kw name="Sleep" owner="BuiltIn">
<msg time="2025-06-09T12:18:26.678935" level="INFO">Slept 2 seconds.</msg>
<arg>2s</arg>
<doc>Pauses the test executed for the given time.</doc>
<status status="PASS" start="2025-06-09T12:18:24.666391" elapsed="2.012544"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-09T12:18:26.681020" level="INFO">✅ Killed process: houston_app.exe</msg>
<arg>✅ Killed process: ${process_name}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-09T12:18:26.679974" elapsed="0.002046"/>
</kw>
<arg>houston_app.exe</arg>
<status status="PASS" start="2025-06-09T12:18:24.393758" elapsed="2.288262"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-06-09T12:18:26.682986" level="INFO">🧹 Environment cleaned</msg>
<arg>🧹 Environment cleaned</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-06-09T12:18:26.682986" elapsed="0.001045"/>
</kw>
<status status="PASS" start="2025-06-09T12:18:22.112832" elapsed="4.571199"/>
</kw>
<status status="PASS" start="2025-06-09T12:17:24.451806" elapsed="62.233232"/>
</test>
<doc>Test to launch Houston Server, HCCA, and type in search field</doc>
<status status="PASS" start="2025-06-09T12:17:23.005382" elapsed="63.684651"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
</tag>
<suite>
<stat name="Get" id="s1" pass="1" fail="0" skip="0">Get</stat>
</suite>
</statistics>
<errors>
</errors>
</robot>
